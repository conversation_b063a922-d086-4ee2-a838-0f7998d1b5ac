<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收起按钮测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background: #f0f0f0;
        }

        /* 控制面板样式 */
        .auto-reply-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 10000;
            background: #fdfdff;
            border-radius: 16px;
            box-shadow: 0 12px 60px rgba(0, 0, 0, 0.25);
            padding: 0;
            width: 1188px;
            max-width: 95vw;
            max-height: 95vh;
            font-family: -apple-system, BlinkMacSystemFont, "Segue UI", Roboto, Helvetica, Arial, sans-serif;
            transition: all 0.3s ease;
            border: 1px solid #e0e6f0;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
            overflow-x: visible;
        }

        .panel-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: visible;
        }

        .panel-main-content {
            display: flex;
            flex-direction: row;
            gap: 20px;
            padding: 20px;
            flex-grow: 1;
            background-color: #f4f7fa;
        }

        .panel-column {
            display: flex;
            flex-direction: column;
            gap: 15px;
            flex: 1;
            padding: 8px;
            background-color: transparent;
            border-radius: 8px;
            min-width: 0;
        }

        /* 收起按钮样式 - 优化后 */
        .collapse-trigger {
            position: fixed; /* 固定定位，完全独立于面板 */
            right: 20px; /* 距离屏幕右边缘20px */
            top: 50%; /* 垂直居中 */
            transform: translateY(-50%);
            width: 40px;
            height: 120px;
            padding: 8px 4px;
            background: linear-gradient(135deg, #868f96 0%, #596164 100%);
            color: white !important;
            border-radius: 8px;
            box-shadow: 2px 0 12px rgba(0, 0, 0, 0.15);
            cursor: pointer;
            font-size: 14px !important;
            font-weight: 500;
            text-align: center;
            border: none;
            transition: all 0.3s ease;
            z-index: 10002;
            writing-mode: vertical-rl;
            text-orientation: mixed;
            display: flex !important;
            align-items: center;
            justify-content: center;
            letter-spacing: 2px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            line-height: 1.4 !important;
            white-space: nowrap;
            overflow: visible;
            text-overflow: visible;
        }

        .collapse-trigger:hover {
            transform: translateY(-50%) translateX(-3px);
            box-shadow: 4px 0 16px rgba(0, 0, 0, 0.2);
            background: linear-gradient(135deg, #596164 0%, #868f96 100%);
            right: 23px;
        }

        /* 缩略图样式 */
        .collapsed-thumbnail {
            display: none;
            position: fixed;
            bottom: 25px;
            right: 25px;
            width: 400px;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.35);
            z-index: 10001;
            cursor: pointer;
            color: white;
            padding: 20px;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .collapsed-thumbnail:hover {
            transform: translateY(-5px) scale(1.03);
            box-shadow: 0 18px 50px rgba(0, 0, 0, 0.4);
        }

        .thumbnail-title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 15px;
            text-shadow: 1px 1px 4px rgba(0,0,0,0.3);
        }

        .thumbnail-subtitle {
            font-size: 20px;
            font-weight: 400;
            opacity: 0.9;
            line-height: 1.4;
            text-shadow: 1px 1px 4px rgba(0,0,0,0.2);
        }

        /* 测试内容样式 */
        .test-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h1>收起按钮位置优化测试</h1>
        <p>这是一个测试页面，用于验证收起按钮是否正确移动到控制面板外部。</p>
        <p><strong>优化内容：</strong></p>
        <ul>
            <li>收起按钮现在使用 <code>position: fixed</code> 完全独立于控制面板</li>
            <li>按钮固定在屏幕右边缘，距离右边20px</li>
            <li>按钮不再依赖面板包装器的相对定位</li>
            <li>悬停效果优化，向左移动而不是向右</li>
        </ul>
        <p>点击下面的按钮来测试收起/展开功能：</p>
        <button onclick="togglePanel()" style="padding: 10px 20px; font-size: 16px; cursor: pointer;">切换面板显示</button>
    </div>

    <!-- 控制面板 -->
    <div class="auto-reply-panel" id="autoReplyPanel">
        <div class="panel-wrapper" id="panelWrapper">
            <div class="panel-main-content">
                <div class="panel-column">
                    <div class="test-content">
                        <h3>第一列</h3>
                        <p>这是控制面板的第一列内容。</p>
                    </div>
                </div>
                <div class="panel-column">
                    <div class="test-content">
                        <h3>第二列</h3>
                        <p>这是控制面板的第二列内容。</p>
                    </div>
                </div>
                <div class="panel-column">
                    <div class="test-content">
                        <h3>第三列</h3>
                        <p>这是控制面板的第三列内容。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 收起按钮 - 现在完全独立于面板 -->
    <button class="collapse-trigger" id="collapseTrigger" title="收起面板">收起知识库</button>

    <!-- 缩略图 -->
    <div class="collapsed-thumbnail" id="collapsedThumbnail" style="display: none;">
        <div class="thumbnail-title">小梅花AI智能客服</div>
        <div class="thumbnail-subtitle">微信小店知识库</div>
    </div>

    <script>
        const autoReplyPanel = document.getElementById('autoReplyPanel');
        const collapseTrigger = document.getElementById('collapseTrigger');
        const collapsedThumbnail = document.getElementById('collapsedThumbnail');

        // 收起按钮事件
        collapseTrigger.addEventListener('click', () => {
            if (autoReplyPanel) autoReplyPanel.style.display = 'none';
            if (collapsedThumbnail) collapsedThumbnail.style.display = 'flex';
            collapseTrigger.textContent = '展开知识库';
            collapseTrigger.title = '展开面板';
        });

        // 缩略图点击事件
        collapsedThumbnail.addEventListener('click', () => {
            if (collapsedThumbnail) collapsedThumbnail.style.display = 'none';
            if (autoReplyPanel) autoReplyPanel.style.display = 'flex';
            collapseTrigger.textContent = '收起知识库';
            collapseTrigger.title = '收起面板';
        });

        // 测试按钮
        function togglePanel() {
            if (autoReplyPanel.style.display === 'none') {
                autoReplyPanel.style.display = 'flex';
                collapsedThumbnail.style.display = 'none';
                collapseTrigger.textContent = '收起知识库';
                collapseTrigger.title = '收起面板';
            } else {
                autoReplyPanel.style.display = 'none';
                collapsedThumbnail.style.display = 'flex';
                collapseTrigger.textContent = '展开知识库';
                collapseTrigger.title = '展开面板';
            }
        }
    </script>
</body>
</html>
